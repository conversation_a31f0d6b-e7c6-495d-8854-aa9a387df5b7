extends CharacterBody2D

enum AIState { STAND, WALK, RUN, ATTACK }
var current_state = AIState.STAND
var target_player: Node2D

@onready var navigation_agent: NavigationAgent2D = $NavigationAgent2D

func _ready():
    # Configure for Quake-style movement
    navigation_agent.avoidance_enabled = true
    navigation_agent.radius = 16.0
    navigation_agent.max_speed = 150.0
    navigation_agent.path_desired_distance = 32.0
    navigation_agent.target_desired_distance = 48.0

func ai_run():
    if target_player:
        navigation_agent.target_position = target_player.global_position
        
        if not navigation_agent.is_navigation_finished():
            var next_pos = navigation_agent.get_next_path_position()
            var desired_velocity = global_position.direction_to(next_pos) * 150.0
            navigation_agent.set_velocity(desired_velocity)

func _on_velocity_computed(safe_velocity: Vector2):
    velocity = safe_velocity
    move_and_slide()
    
    # Update sprite direction (Quake-style)
    if velocity.length() > 10:
        sprite.flip_h = velocity.x < 0